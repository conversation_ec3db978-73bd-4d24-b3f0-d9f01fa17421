plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "me.ahmetcetinkaya.whph"

    compileSdk 35

    ndkVersion = flutter.ndkVersion

    // Reproducible Build Configuration
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    // Enable BuildConfig feature for custom fields
    buildFeatures {
        buildConfig = true
    }

    // Reproducible Build Settings
    packagingOptions {
        // Exclude files that can cause non-reproducible builds
        excludes += [
            '**/META-INF/DEPENDENCIES',
            '**/META-INF/LICENSE',
            '**/META-INF/LICENSE.txt',
            '**/META-INF/NOTICE',
            '**/META-INF/NOTICE.txt',
            '**/META-INF/*.kotlin_module',
            '**/META-INF/versions/**',
            '**/META-INF/gradle/**',
            '**/kotlin/**',
            '**/META-INF/proguard/**',
            'DebugProbesKt.bin',
            'kotlin-tooling-metadata.json',
            // Exclude Windows-specific assets
            '**/assets/**/*.ico',
            '**/assets/**/*.exe',
            '**/assets/**/*.dll',
            // Exclude unnecessary locales
            '**/assets/flutter_assets/packages/flutter_localizations/assets/material_*/symbols/',
            // Exclude development assets
            '**/assets/**/*.map',
            '**/assets/**/*.dev',
            '**/assets/**/*.test'
        ]
        
        // Make timestamps deterministic
        doNotStrip '**/*.so'
        // Merge duplicate files
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        pickFirst '**/libfbjni.so'
    }

    // Ensure deterministic builds
    configurations.all {
        resolutionStrategy {
            // Force specific versions to avoid dynamic resolution
            force 'androidx.core:core-ktx:1.12.0'
            force 'androidx.work:work-runtime-ktx:2.9.0'
        }
    }

    defaultConfig {
        applicationId = "me.ahmetcetinkaya.whph"
        minSdk = 22
        targetSdk = 35
        // Try to get VERSION_CODE from environment variable, otherwise use flutter.versionCode
        def customVersionCode = System.getenv('VERSION_CODE')
        versionCode = customVersionCode ? customVersionCode.toInteger() : flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders += [
            'ENABLE_IMPELLER': 'false'
        ]
        
        // Default to NOT using Google Play Services (F-Droid compatible)
        resValue 'bool', 'use_google_play_services', 'false'
        
        ndk {
            // Architecture filtering handled by splits configuration
        }
    }

    dependenciesInfo {
        // Disables dependency metadata when building APKs.
        includeInApk = false
        // Disables dependency metadata when building Android App Bundles.
        includeInBundle = false
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    // Enable APK splitting by architecture
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a', 'armeabi-v7a', 'x86_64'
            universalApk false
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            // Use both regular and F-Droid ProGuard rules for maximum Google Play removal
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'proguard-rules-fdroid.pro'

            // Reproducible Build Settings for Release
            zipAlignEnabled true
            debuggable false

            // Disable Google Play Services for F-Droid compatibility
            resValue 'bool', 'use_google_play_services', 'false'

            // Disable build metadata to improve reproducibility
            buildConfigField "String", "BUILD_TIME", '""'
            buildConfigField "String", "GIT_HASH", '""'
        }



        debug {
            // Debug build configuration
            debuggable true
            minifyEnabled false
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}

flutter {
    source = "../.."
}

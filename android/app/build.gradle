plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "me.ahmetcetinkaya.whph"

    compileSdk 35

    ndkVersion = flutter.ndkVersion

    // Reproducible Build Configuration
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    // Enable BuildConfig feature for custom fields
    buildFeatures {
        buildConfig = false  // Disable BuildConfig to avoid timestamps
    }

    // Reproducible Build Settings
    packagingOptions {
        // Exclude files that can cause non-reproducible builds
        excludes += [
            '**/META-INF/DEPENDENCIES',
            '**/META-INF/LICENSE',
            '**/META-INF/LICENSE.txt',
            '**/META-INF/NOTICE',
            '**/META-INF/NOTICE.txt',
            '**/META-INF/*.kotlin_module'
        ]
        // Make timestamps deterministic
        doNotStrip '**/*.so'
        // Use legacy packaging for stable binary layout
        jniLibs.useLegacyPackaging = true
    }

    // Ensure deterministic builds
    configurations.all {
        resolutionStrategy {
            // Force specific versions to avoid dynamic resolution
            force 'androidx.core:core-ktx:1.12.0'
            force 'androidx.work:work-runtime-ktx:2.9.0'
        }
    }

    defaultConfig {
        applicationId = "me.ahmetcetinkaya.whph"
        minSdk = 22
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders += [
            'ENABLE_IMPELLER': 'false'
        ]
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        }
    }

    dependenciesInfo {
        // Disables dependency metadata when building APKs.
        includeInApk = false
        // Disables dependency metadata when building Android App Bundles.
        includeInBundle = false
    }

    signingConfigs {
        release {
            if (keystoreProperties['keyAlias'] && keystoreProperties['storeFile']) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        release {
            // Only apply signing if keystore properties are available
            if (keystoreProperties['keyAlias'] && keystoreProperties['storeFile']) {
                signingConfig signingConfigs.release
            }
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            
            // Reproducible Build Settings for Release
            zipAlignEnabled true
            debuggable false
        }
        
        debug {
            // Debug build configuration
            debuggable true
            minifyEnabled false
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}

flutter {
    source = "../.."
}

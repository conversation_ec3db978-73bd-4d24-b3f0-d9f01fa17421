# F-Droid specific proguard rules to exclude Google Play dependencies
# This file is used only for F-Droid builds to ensure complete removal of Google Play services

# AGGRESSIVE GOOGLE PLAY CORE REMOVAL FOR F-DROID
# These rules use multiple strategies to ensure complete removal

# Strategy 1: Remove all Google Play Core classes completely
-assumenosideeffects class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# Strategy 2: Remove Google Play Services
-assumenosideeffects class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Strategy 3: Explicitly remove the specific problematic classes detected by F-Droid
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }
-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }

# Strategy 4: Remove all references and suppress warnings
-dontwarn com.google.android.play.core.splitinstall.SplitInstallSessionState
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManager
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener

# Strategy 5: Remove entire Google Play Core packages
-assumenosideeffects class com.google.android.play.core.splitinstall.** { *; }
-assumenosideeffects class com.google.android.play.core.tasks.** { *; }
-assumenosideeffects class com.google.android.play.core.splitcompat.** { *; }
-assumenosideeffects class com.google.android.play.core.appupdate.** { *; }
-assumenosideeffects class com.google.android.play.core.review.** { *; }
-assumenosideeffects class com.google.android.play.core.common.** { *; }

# Strategy 6: Remove warnings for all Google Play Core packages
-dontwarn com.google.android.play.core.splitinstall.**
-dontwarn com.google.android.play.core.tasks.**
-dontwarn com.google.android.play.core.splitcompat.**
-dontwarn com.google.android.play.core.appupdate.**
-dontwarn com.google.android.play.core.review.**
-dontwarn com.google.android.play.core.common.**

# Strategy 7: Remove any remaining Google Play references
-dontwarn com.google.android.play.**
-assumenosideeffects class com.google.android.play.** { *; }

# Strategy 8: Force removal using keep rules (inverted logic)
-keep,allowshrinking,allowoptimization,allowobfuscation class !com.google.android.play.core.** { *; }

# Strategy 9: Additional aggressive removal for stubborn classes
-assumenosideeffects class * extends com.google.android.play.core.** { *; }
-assumenosideeffects class * implements com.google.android.play.core.** { *; }

# Strategy 10: Remove any Google Play Core related resources
-dontwarn **.R$*
-dontwarn com.google.android.play.core.R$*

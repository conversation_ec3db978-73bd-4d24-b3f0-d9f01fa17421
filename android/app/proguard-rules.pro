# App Usage Plugin
-keep class dk.cachet.app_usage.** { *; }
-keepclassmembers class dk.cachet.app_usage.** { *; }

# Aggressive Google services removal for F-Droid compatibility
# Remove ALL Google Play Services and Google Play Core classes
-assumenosideeffects class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Remove ALL Google Play Core classes
-assumenosideeffects class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# SELECTIVE Google classes removal (keep useful libraries like Gson, Guava, ZXing)
# Remove Google Play services but keep other Google libraries that are useful
-assumenosideeffects class com.google.android.play.** { *; }
-dontwarn com.google.android.play.**

# Keep useful Google libraries but remove Play services
-keep class com.google.gson.** { *; }
-keep class com.google.guava.** { *; }
-keep class com.google.zxing.** { *; }
-keep class com.google.errorprone.** { *; }

# Additional specific exclusions for problematic classes
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallManager { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnFailureListener { *; }
-assumenosideeffects class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-assumenosideeffects class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }
-assumenosideeffects class com.google.android.play.core.tasks.OnSuccessListener { *; }

# Comprehensive warning suppression for Google Play packages
-dontwarn com.google.android.play.**
-dontwarn com.google.android.gms.**
-dontwarn com.google.firebase.**

# Flutter optimizations
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Aggressive optimizations
-allowaccessmodification
-mergeinterfacesaggressively
-optimizationpasses 5
-overloadaggressively
-repackageclasses ''

# Remove unused code
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Dart/Flutter specific optimizations
-dontwarn io.flutter.embedding.**
-dontwarn io.flutter.plugin.**

# Database optimizations
-keep class org.sqlite.** { *; }
-keep class androidx.sqlite.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Remove debug information
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

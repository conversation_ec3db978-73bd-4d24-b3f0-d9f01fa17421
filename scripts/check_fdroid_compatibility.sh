#!/bin/bash

# F-Droid Compatibility Checker for Android APK
# This script checks for Google Play Core classes that might cause F-Droid build failures

APK_PATH="$1"
if [ -z "$APK_PATH" ]; then
    echo "Usage: $0 <path-to-apk>"
    echo "Example: $0 build/app/outputs/flutter-apk/app-arm64-v8a-release.apk"
    exit 1
fi

if [ ! -f "$APK_PATH" ]; then
    echo "Error: APK file not found: $APK_PATH"
    exit 1
fi

echo "🔍 Checking F-Droid compatibility for: $APK_PATH"
echo "=================================================="

# Create temporary directory for analysis
TEMP_DIR=$(mktemp -d)
echo "📁 Extracting APK to: $TEMP_DIR"

# Extract APK
unzip -q "$APK_PATH" -d "$TEMP_DIR"

echo ""
echo "🔍 Searching for Google Play Core classes..."
echo "=============================================="

# Check for specific problematic classes
PROBLEMATIC_CLASSES=(
    "com/google/android/play/core/splitinstall/SplitInstallSessionState"
    "com/google/android/play/core/splitinstall/SplitInstallManager"
    "com/google/android/play/core/tasks/OnFailureListener"
    "com/google/android/play/core/splitcompat/SplitCompatApplication"
    "com/google/android/play/core/splitinstall/SplitInstallStateUpdatedListener"
    "com/google/android/play/core/tasks/OnSuccessListener"
)

FOUND_CLASSES=0
for class in "${PROBLEMATIC_CLASSES[@]}"; do
    if find "$TEMP_DIR" -name "*.dex" -exec strings {} \; | grep -q "$class"; then
        echo "❌ Found: $class"
        FOUND_CLASSES=$((FOUND_CLASSES + 1))
    else
        echo "✅ Clean: $class"
    fi
done

echo ""
echo "📊 Summary:"
echo "==========="
echo "Total problematic classes found: $FOUND_CLASSES"

# Check for any Google Play Core references
TOTAL_GOOGLE_PLAY_REFS=$(find "$TEMP_DIR" -name "*.dex" -exec strings {} \; | grep -i "com/google/android/play/core" | wc -l)
echo "Total Google Play Core references: $TOTAL_GOOGLE_PLAY_REFS"

echo ""
if [ $FOUND_CLASSES -eq 0 ]; then
    echo "🎉 SUCCESS: APK appears to be F-Droid compatible!"
    echo "   No problematic Google Play Core classes found."
elif [ $FOUND_CLASSES -le 2 ] && [ $TOTAL_GOOGLE_PLAY_REFS -le 15 ]; then
    echo "⚠️  WARNING: APK might be F-Droid compatible with minor issues"
    echo "   Found $FOUND_CLASSES problematic classes, but they might be acceptable."
    echo "   Consider submitting to F-Droid for testing."
else
    echo "❌ FAILURE: APK likely NOT F-Droid compatible"
    echo "   Too many Google Play Core references found."
    echo "   Additional steps needed to remove these dependencies."
fi

echo ""
echo "🔧 Recommendations:"
echo "==================="
if [ $FOUND_CLASSES -gt 0 ]; then
    echo "1. Try submitting the current APK to F-Droid - some minimal references might be acceptable"
    echo "2. Consider replacing packages that bring in Google Play dependencies"
    echo "3. Use more aggressive ProGuard rules or manual APK modification"
else
    echo "1. APK looks good for F-Droid submission!"
    echo "2. Test the APK thoroughly to ensure all functionality works"
fi

# Cleanup
rm -rf "$TEMP_DIR"

echo ""
echo "✅ Analysis complete!"

#!/bin/bash

# Version bump script for WHPH project
# Updates version in pubspec.yaml, app_info.dart, installer.iss files and generates changelog
#
# F-Droid Version Code Handling:
# - Supports multi-architecture builds with incremental version codes
# - Ensures CurrentVersionCode is always set to the highest version code used
# - Prevents F-Droid build failures due to version code regression
# - Validates version code consistency before and after updates

set -e

# Function to validate F-Droid version code consistency
validate_fdroid_version_codes() {
    local fdroid_file="$1"

    if [ ! -f "$fdroid_file" ]; then
        echo "Warning: F-Droid metadata file not found at $fdroid_file"
        return 1
    fi

    # Extract all version codes from the F-Droid metadata
    local version_codes=($(grep "versionCode:" "$fdroid_file" | sed 's/.*versionCode: //' | sort -n))
    local current_version_code=$(grep "^CurrentVersionCode:" "$fdroid_file" | sed 's/CurrentVersionCode: //')

    if [ ${#version_codes[@]} -gt 0 ]; then
        local highest_version_code=${version_codes[-1]}

        echo "F-Droid version code validation:"
        echo "  Build version codes: ${version_codes[*]}"
        echo "  CurrentVersionCode: $current_version_code"
        echo "  Highest build version code: $highest_version_code"

        if [ "$current_version_code" -ne "$highest_version_code" ]; then
            echo "  ⚠️  WARNING: CurrentVersionCode ($current_version_code) does not match highest build version code ($highest_version_code)"
            echo "  This may cause F-Droid build failures due to version code regression"
            return 1
        else
            echo "  ✅ F-Droid version codes are consistent"
        fi
    fi

    return 0
}

# Check if argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [major|minor|patch]"
    exit 1
fi

BUMP_TYPE=$1

# Validate bump type
if [[ "$BUMP_TYPE" != "major" && "$BUMP_TYPE" != "minor" && "$BUMP_TYPE" != "patch" ]]; then
    echo "Error: Invalid bump type. Use 'major', 'minor', or 'patch'"
    exit 1
fi

# Get project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# File paths
PUBSPEC_FILE="$PROJECT_ROOT/pubspec.yaml"
APP_INFO_FILE="$PROJECT_ROOT/lib/src/core/domain/shared/constants/app_info.dart"
INSTALLER_FILE="$PROJECT_ROOT/windows/setup-wizard/installer.iss"
FDROID_METADATA_FILE="$PROJECT_ROOT/android/fdroid/metadata/me.ahmetcetinkaya.whph.yml"

# Validate current F-Droid version code consistency before making changes
echo "Validating current F-Droid version code consistency..."
if ! validate_fdroid_version_codes "$FDROID_METADATA_FILE"; then
    echo ""
    echo "⚠️  Current F-Droid metadata has version code inconsistencies."
    echo "The script will fix these issues during the version bump process."
    echo ""
fi

# Extract current version from pubspec.yaml
CURRENT_VERSION=$(grep "^version:" "$PUBSPEC_FILE" | sed 's/version: //' | sed 's/+.*//')

echo "Current version: $CURRENT_VERSION"

# Split version into components
IFS='.' read -r -a VERSION_PARTS <<< "$CURRENT_VERSION"
MAJOR=${VERSION_PARTS[0]}
MINOR=${VERSION_PARTS[1]}
PATCH=${VERSION_PARTS[2]}

echo "Current: $MAJOR.$MINOR.$PATCH"

# Bump version based on type
case $BUMP_TYPE in
    "major")
        MAJOR=$((MAJOR + 1))
        MINOR=0
        PATCH=0
        ;;
    "minor")
        MINOR=$((MINOR + 1))
        PATCH=0
        ;;
    "patch")
        PATCH=$((PATCH + 1))
        ;;
esac

NEW_VERSION="$MAJOR.$MINOR.$PATCH"
echo "New version: $NEW_VERSION"

# Get current build number from pubspec.yaml
CURRENT_BUILD=$(grep "^version:" "$PUBSPEC_FILE" | sed 's/.*+//')
NEW_BUILD=$((CURRENT_BUILD + 1))

# Calculate F-Droid version codes for multi-architecture builds
# We use incremental version codes for different architectures to ensure uniqueness
FDROID_VERSION_CODE_X86_64=$NEW_BUILD
FDROID_VERSION_CODE_ARM_V7A=$((NEW_BUILD + 1))
FDROID_VERSION_CODE_ARM64_V8A=$((NEW_BUILD + 2))

# The CurrentVersionCode should be the highest version code used
FDROID_CURRENT_VERSION_CODE=$FDROID_VERSION_CODE_ARM64_V8A

echo "F-Droid version codes:"
echo "  x86_64: $FDROID_VERSION_CODE_X86_64"
echo "  armeabi-v7a: $FDROID_VERSION_CODE_ARM_V7A"
echo "  arm64-v8a: $FDROID_VERSION_CODE_ARM64_V8A"
echo "  CurrentVersionCode: $FDROID_CURRENT_VERSION_CODE"

# Update pubspec.yaml
echo "Updating $PUBSPEC_FILE..."
sed -i "s/^version:.*/version: $NEW_VERSION+$NEW_BUILD/" "$PUBSPEC_FILE"

# Update app_info.dart
echo "Updating $APP_INFO_FILE..."
sed -i "s/static const String version = \".*\";/static const String version = \"$NEW_VERSION\";/" "$APP_INFO_FILE"

# Update installer.iss
echo "Updating $INSTALLER_FILE..."
sed -i "s/AppVersion=.*/AppVersion=$NEW_VERSION/" "$INSTALLER_FILE"

# Update F-Droid metadata with proper version code synchronization
echo "Updating $FDROID_METADATA_FILE (initial)..."

# Validate that F-Droid metadata file exists
if [ ! -f "$FDROID_METADATA_FILE" ]; then
    echo "Error: F-Droid metadata file not found at $FDROID_METADATA_FILE"
    exit 1
fi

# Get current F-Droid CurrentVersionCode to ensure monotonic increase
CURRENT_FDROID_VERSION_CODE=$(grep "^CurrentVersionCode:" "$FDROID_METADATA_FILE" | sed 's/CurrentVersionCode: //')
echo "Current F-Droid CurrentVersionCode: $CURRENT_FDROID_VERSION_CODE"

# Ensure new version codes are higher than current F-Droid version code
if [ "$FDROID_CURRENT_VERSION_CODE" -le "$CURRENT_FDROID_VERSION_CODE" ]; then
    echo "Warning: New F-Droid version code ($FDROID_CURRENT_VERSION_CODE) is not higher than current ($CURRENT_FDROID_VERSION_CODE)"
    echo "Adjusting version codes to ensure monotonic increase..."

    # Calculate new base version code that's higher than current
    NEW_BASE=$((CURRENT_FDROID_VERSION_CODE + 1))
    FDROID_VERSION_CODE_X86_64=$NEW_BASE
    FDROID_VERSION_CODE_ARM_V7A=$((NEW_BASE + 1))
    FDROID_VERSION_CODE_ARM64_V8A=$((NEW_BASE + 2))
    FDROID_CURRENT_VERSION_CODE=$FDROID_VERSION_CODE_ARM64_V8A

    # Also update the main app version code to match
    NEW_BUILD=$NEW_BASE

    echo "Adjusted F-Droid version codes:"
    echo "  x86_64: $FDROID_VERSION_CODE_X86_64"
    echo "  armeabi-v7a: $FDROID_VERSION_CODE_ARM_V7A"
    echo "  arm64-v8a: $FDROID_VERSION_CODE_ARM64_V8A"
    echo "  CurrentVersionCode: $FDROID_CURRENT_VERSION_CODE"
    echo "  Main app version code: $NEW_BUILD"
fi

# Update all three architecture builds with incremental version codes
sed -i "s/versionName: .*/versionName: $NEW_VERSION/g" "$FDROID_METADATA_FILE"

# Update version codes for each architecture specifically
# x86_64 (first occurrence)
sed -i "0,/versionCode: .*/{s/versionCode: .*/versionCode: $FDROID_VERSION_CODE_X86_64/}" "$FDROID_METADATA_FILE"
# armeabi-v7a (second occurrence)
awk -v new_code="$FDROID_VERSION_CODE_ARM_V7A" '
/versionCode: / && ++count == 2 {
    sub(/versionCode: .*/, "versionCode: " new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"
# arm64-v8a (third occurrence)
awk -v new_code="$FDROID_VERSION_CODE_ARM64_V8A" '
/versionCode: / && ++count == 3 {
    sub(/versionCode: .*/, "versionCode: " new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"

# Update VERSION_CODE environment variables in build sections
# x86_64 (first occurrence)
sed -i "0,/export VERSION_CODE=.*/{s/export VERSION_CODE=.*/export VERSION_CODE=$FDROID_VERSION_CODE_X86_64/}" "$FDROID_METADATA_FILE"
# armeabi-v7a (second occurrence)
awk -v new_code="$FDROID_VERSION_CODE_ARM_V7A" '
/export VERSION_CODE=/ && ++count == 2 {
    sub(/export VERSION_CODE=.*/, "export VERSION_CODE=" new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"
# arm64-v8a (third occurrence)
awk -v new_code="$FDROID_VERSION_CODE_ARM64_V8A" '
/export VERSION_CODE=/ && ++count == 3 {
    sub(/export VERSION_CODE=.*/, "export VERSION_CODE=" new_code)
}
{print}
' "$FDROID_METADATA_FILE" > "$FDROID_METADATA_FILE.tmp" && mv "$FDROID_METADATA_FILE.tmp" "$FDROID_METADATA_FILE"

# Update CurrentVersion and CurrentVersionCode with the highest version code
# This is critical to prevent F-Droid "version code regression" errors
sed -i "s/CurrentVersion: .*/CurrentVersion: $NEW_VERSION+$FDROID_CURRENT_VERSION_CODE/" "$FDROID_METADATA_FILE"
sed -i "s/CurrentVersionCode: .*/CurrentVersionCode: $FDROID_CURRENT_VERSION_CODE/" "$FDROID_METADATA_FILE"

echo "F-Droid metadata updated successfully:"
echo "  CurrentVersion: $NEW_VERSION+$FDROID_CURRENT_VERSION_CODE"
echo "  CurrentVersionCode: $FDROID_CURRENT_VERSION_CODE"

# Validate the updated F-Droid metadata
echo ""
echo "Validating updated F-Droid metadata..."
if validate_fdroid_version_codes "$FDROID_METADATA_FILE"; then
    echo "✅ F-Droid version code validation passed"
else
    echo "❌ F-Droid version code validation failed"
    echo "Please check the F-Droid metadata file manually"
    exit 1
fi

# Generate changelog
echo "Generating changelog..."
cd "$PROJECT_ROOT"
bash scripts/create_changelog.sh "$NEW_BUILD" --auto

# Git operations - Create version bump commit first
echo "Creating version bump commit..."

# First, stage changes in the F-Droid submodule
echo "Staging F-Droid metadata changes in submodule..."
cd "$PROJECT_ROOT/android/fdroid"
git add "metadata/me.ahmetcetinkaya.whph.yml"

echo ""
echo "About to commit F-Droid metadata changes with message:"
echo "  feat(me.ahmetcetinkaya.whph): update app version to $NEW_VERSION"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "feat(me.ahmetcetinkaya.whph): update app version to $NEW_VERSION"
    echo "✓ F-Droid metadata commit created"
else
    echo "❌ F-Droid metadata commit cancelled"
    exit 1
fi

cd "$PROJECT_ROOT"

# Then, stage changes in the main repository (including submodule update)
echo "Staging main repository changes..."
git add "$PUBSPEC_FILE" "$APP_INFO_FILE" "$INSTALLER_FILE" "android/fdroid" "CHANGELOG.md" "fastlane/metadata/android/en-US/changelogs/"

echo ""
echo "About to commit main repository changes with message:"
echo "  chore: update app version to $NEW_VERSION"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "chore: update app version to $NEW_VERSION"
    echo "✓ Main repository commit created"
else
    echo "❌ Main repository commit cancelled"
    exit 1
fi

# Get the commit hash of the version bump commit
VERSION_COMMIT=$(git rev-parse HEAD)

# Now update F-Droid metadata with the correct commit hash
echo "Updating F-Droid metadata with commit hash..."
cd "$PROJECT_ROOT/android/fdroid"
# Update all three architecture builds with the same commit hash
sed -i "s/commit: .*/commit: $VERSION_COMMIT/g" "metadata/me.ahmetcetinkaya.whph.yml"
git add "metadata/me.ahmetcetinkaya.whph.yml"

echo ""
echo "About to commit F-Droid metadata with correct commit hash:"
echo "  build(me.ahmetcetinkaya.whph): update commit hash for F-Droid build"
echo "  Commit hash: $VERSION_COMMIT"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "build(me.ahmetcetinkaya.whph): update commit hash for F-Droid build"
    echo "✓ F-Droid commit hash update completed"
else
    echo "❌ F-Droid commit hash update cancelled"
    exit 1
fi

cd "$PROJECT_ROOT"

# Update the submodule reference in main repo
git add "android/fdroid"

echo ""
echo "About to commit submodule update with message:"
echo "  chore: update F-Droid submodule with commit hash"
echo ""
read -p "Do you want to proceed with this commit? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git commit -m "chore: update F-Droid submodule with commit hash"
    echo "✓ Submodule update commit created"
else
    echo "❌ Submodule update commit cancelled"
    exit 1
fi

# Create version tag
echo ""
echo "About to create version tag:"
echo "  Tag: v$NEW_VERSION"
echo "  Message: Version $NEW_VERSION"
echo ""
read -p "Do you want to create this tag? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git tag -a "v$NEW_VERSION" -m "Version $NEW_VERSION"
    echo "✓ Version tag created: v$NEW_VERSION"
else
    echo "❌ Version tag creation cancelled"
    exit 1
fi

echo "Version bump completed successfully!"
echo "Updated files:"
echo "  - $PUBSPEC_FILE (version: $NEW_VERSION+$NEW_BUILD)"
echo "  - $APP_INFO_FILE (version: $NEW_VERSION)"
echo "  - $INSTALLER_FILE (version: $NEW_VERSION)"
echo "  - $FDROID_METADATA_FILE:"
echo "    * versionName: $NEW_VERSION"
echo "    * versionCodes: x86_64=$FDROID_VERSION_CODE_X86_64, armeabi-v7a=$FDROID_VERSION_CODE_ARM_V7A, arm64-v8a=$FDROID_VERSION_CODE_ARM64_V8A"
echo "    * CurrentVersionCode: $FDROID_CURRENT_VERSION_CODE (highest version code)"
echo "    * CurrentVersion: $NEW_VERSION+$FDROID_CURRENT_VERSION_CODE"
echo "    * commit: $VERSION_COMMIT"
echo "  - CHANGELOG.md (generated for version $NEW_VERSION)"
echo "  - fastlane/metadata/android/en-US/changelogs/ (generated for version code $NEW_BUILD)"
echo ""

# Git operations completed
echo "Git operations completed:"
echo "  - Created version bump commit: $VERSION_COMMIT"
echo "  - Updated F-Droid metadata with correct commit hash"
echo "  - Created version tag: v$NEW_VERSION"
echo ""
echo "To push changes and tags to remote:"
echo "  rps version:push"
echo ""
echo "This will:"
echo "  1. Push F-Droid submodule changes"
echo "  2. Push main repository changes and tags"
